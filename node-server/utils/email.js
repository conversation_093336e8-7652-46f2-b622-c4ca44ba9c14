const mailchimp = require('@mailchimp/mailchimp_transactional')(process.env.MANDRILL_KEY || '');
const Mustache = require('mustache');
const fs = require('fs')

const { SESClient, SendEmailCommand } = require("@aws-sdk/client-ses");


const sesClient = new SESClient({
    region: "us-east-1",
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY || '',
        secretAccessKey: process.env.AWS_SECRET || ''
    }
});

const sendEmailTemplateSES = async (name, email, subject, template, data, fromEmail = '<EMAIL>', fromName = "Sitefotos", site = 'www.sitefotos.com', replyTo = '<EMAIL>') => {
    try {
        const html = Mustache.render(fs.readFileSync(template, 'utf8'), data);

        const params = {
            Destination: {
                ToAddresses: [email]
            },
            Message: {
                Body: {
                    Html: {
                        Charset: "UTF-8",
                        Data: html
                    }
                },
                Subject: {
                    Charset: "UTF-8",
                    Data: subject
                }
            },
            Source: `${fromName} <${fromEmail}>`,
            ReplyToAddresses: [replyTo],
            Tags: [
                {
                    Name: "website",
                    Value: site
                }
            ]
        };

        const command = new SendEmailCommand(params);
        const result = await sesClient.send(command);
        return result;
    } catch (ex) {
        console.error(ex.message || ex);
        return null;
    }
};

const sendEmailTemplate = async (name, email, subject, template, data, fromEmail = '<EMAIL>', fromName = "Sitefotos", site = 'www.sitefotos.com', replyTo = '<EMAIL>') => {
    try {
        const html = Mustache.render(fs.readFileSync(template, 'utf8'), data)
        let msg = {
            'html': html,
            'subject': subject,
            'from_email': fromEmail,
            'from_name': fromName,
            'to': [
                {
                    'email': email,
                    'name': name,
                    'type': 'to'
                }
            ],
            'headers':
            {
                'Reply-To': replyTo
            },
            'important': false,
            'track_opens': true,
            'track_clicks': true,
            'auto_text': null,
            'auto_html': null,
            'inline_css': null,
            'url_strip_qs': null,
            'preserve_recipients': null,
            'view_content_link': null,
            'bcc_address': null,
            'tracking_domain': null,
            'signing_domain': null,
            'return_path_domain': null,
            'merge': true,
            'merge_language': 'mailchimp',
            'metadata': {
                'website': site
            }
        }

        let result = await mailchimp.messages.send({ message: msg })
        return result;
    } catch (ex) {
        console.error(ex.message || ex);
        return null;
    }
}

module.exports = {
    sendEmailTemplate,sendEmailTemplateSES
}
