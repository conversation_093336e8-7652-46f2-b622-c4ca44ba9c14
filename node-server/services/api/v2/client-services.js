const { awaitSafe<PERSON><PERSON>y, insertObj, updateObj, awaitQuery } = require('../../../utils/db');
const { encodeIdShort, decodeIdShort } = require('../../../utils/common');
const bus = require('../../../utils/eventbus');
const { createService, activateService } = require('../../service.services');
const { syncForm } = require('../../form.services');
const { sendEmailTemplateSES } = require('../../../utils/email');


const discoverTrades = async (clientVendorId) => {
  const trades = await awaitSafeQuery('SELECT st_id, st_trade FROM sitefotos_trades', []);
  return trades.map(trade => ({
    trade_id: trade.st_id,
    trade_name: trade.st_trade,
  }));
};


const discoverServiceTypes = async (clientVendorId) => {

  const serviceTypes = await awaitSafeQuery(
    'SELECT sst_id, sst_service_type, sst_trade_id, sst_category FROM sitefotos_service_types ORDER BY sst_service_type ASC',
    []
  );
  return serviceTypes.map(st => ({
    service_type_id: st.sst_id,
    name: st.sst_service_type,
    trade_id: st.sst_trade_id,
  }));
};



const discoverVendors = async (clientVendorId) => {

  const query = `
    SELECT DISTINCT
      mv.vendor_id,
      mv.vendor_company_name,
      mv.vendor_email,
      mv.vendor_phone
    FROM maptile_vendors mv
    JOIN sitefotos_site_client_mapping sscm ON mv.vendor_id = sscm.sscm_vendor_id
    JOIN sitefotos_client_sites scs ON sscm.sscm_client_site_id = scs.scs_id
    WHERE scs.scs_client_vendor_id = ? 
    ORDER BY mv.vendor_company_name ASC;
  `;


  const performingVendors = await awaitSafeQuery(query, [clientVendorId]);

  return performingVendors.map(vendor => ({
    sitefotos_internal_vendor_id: encodeIdShort(vendor.vendor_id, 'maptile_vendors'),
    vendor_name: vendor.vendor_company_name,
    contact_email: vendor.vendor_email,
    phone_number: vendor.vendor_phone,
  }));
};


const discoverSites = async (clientVendorId, filterSitefotosInternalVendorId = null) => {
  let performingVendorDbId = null;
  if (filterSitefotosInternalVendorId) {
    performingVendorDbId = decodeIdShort(filterSitefotosInternalVendorId);
    if (isNaN(performingVendorDbId)) {
      console.error('Invalid filterSitefotosInternalVendorId:', filterSitefotosInternalVendorId);
      return [];
    }
  }

  let queryParams = [clientVendorId];
  let query = /*SQL*/`SELECT scs_client_internal_id AS primary_site_identifier, sscm_vendor_id AS performing_vendor_id, mb_id AS sitefotos_db_site_id, mb_nickname AS site_name, mb_address1 AS address_line1, mb_zip_code AS zip_code, City AS city, state_abv_name AS state from sitefotos_site_client_mapping_extended_ungrouped LEFT JOIN maptile_building ON sscm_site_id = mb_id LEFT JOIN maptile_city ON mb_city = CityId LEFT JOIN maptile_state ON mb_state = id WHERE scs_client_vendor_id = ?`;

  if (performingVendorDbId !== null) {
    query += ` AND sscm_vendor_id = ?`;
    queryParams.push(performingVendorDbId);
  }

  query += ` ORDER BY scs_client_internal_id ASC;`;

  const sites = await awaitSafeQuery(query, queryParams);

  return sites.map(site => ({
    primary_site_identifier: site.primary_site_identifier,
    sitefotos_internal_vendor_id: encodeIdShort(site.performing_vendor_id, 'maptile_vendors'),
    sitefotos_internal_site_id: encodeIdShort(site.sitefotos_db_site_id, 'maptile_building'),
    site_name: site.site_name,
    address_line1: site.address_line1,
    city: site.city,
    state: site.state,
    zip_code: site.zip_code,
  }));
};



const workOrderServices = require('../../workorder.services');
const apiWorkOrderServices = require('../../api-workorder-services');


const createClientWorkOrder = async (clientVendorId, workOrderData) => {
  console.log('createClientWorkOrder', workOrderData);
  const performingVendorDbId = decodeIdShort(workOrderData.sitefotos_internal_vendor_id);
  const siteDbId = decodeIdShort(workOrderData.sitefotos_internal_site_id);
  console.log('performingVendorDbId', performingVendorDbId);
  console.log('siteDbId', siteDbId);
  if (isNaN(performingVendorDbId) || isNaN(siteDbId)) {
    throw new Error('Invalid performing vendor ID or site ID.');
  }

  const {
    external_wo_id,
    description,
    trade_id, 
    scheduled_date,
    services,
    close_on_submit,
    form_template_id,
  } = workOrderData.work_order_details;

  let formTemplateId = null;
  if (form_template_id) {
      const decodedFormTemplateId = decodeIdShort(form_template_id);
      if(isNaN(decodedFormTemplateId)) {
        throw new Error('Invalid form_template_id.');
      }
      formTemplateId = await syncForm(decodedFormTemplateId, performingVendorDbId);
  }

  
  const validatedServiceIdsForWO = [];
  if(services) {
  for (const service of services) {
   
   
    const vendorServiceLookup = await awaitSafeQuery(
      `SELECT vs_service_id, vs_service_status FROM vendor_services
       WHERE vs_vendor_id = ? AND vs_service_name = ? AND vs_trade_id = ? and vs_service_type_id = ?`, 
      [performingVendorDbId, service.service_name, trade_id, service.service_type_id]
    );
    if (!vendorServiceLookup.length) {
      const optionsObject = {
        servicetype: 'simple',
        servicedetail: false,
        detailmultiple: false,
        servicedetailloptions: ''
      };
      const data = {
        name: service.service_name,
        serviceOptions: JSON.stringify(optionsObject),
        provider: 'ServiceChannel',
        serviceTypeID: service.service_type_id,
        tradeID: trade_id,
      };

      const id = await createService(performingVendorDbId, data);
      validatedServiceIdsForWO.push(id);
    }
    else {
      validatedServiceIdsForWO.push(vendorServiceLookup[0].vs_service_id);
    }
  }
}

 
  let woStatusString = 'OPEN';
  if (scheduled_date) {
      const nowUnix = Math.floor(Date.now() / 1000);
      if (scheduled_date >= nowUnix) {
          woStatusString = 'SCHEDULED';
      }
  }

  const woPayload = {
      systemID: 1, 
      selectedSite: siteDbId,
      tradeID: trade_id, 
      woDescription: description,
      woNumber: external_wo_id,
      scheduledDate: scheduled_date,
      services: validatedServiceIdsForWO, 
      closeOnSubmit: close_on_submit, 
      contacts: ["-1"], 
      formTemplate: formTemplateId,
      priority: null,
      categoryID: null,
      nte: null,
      contractorNte: null,
      completionDate: null, 
      requestedDate: Math.floor(Date.now() / 1000),
      status: woStatusString, 
      bulk: 0,
      clientVendorId: clientVendorId,
  };

  const newWoId = await workOrderServices.saveWorkOrder(woPayload, performingVendorDbId);

  if (!newWoId) {
      throw new Error('Failed to create work order using workOrderServices.saveWorkOrder.');
  }


  return {
      sitefotos_work_order_id: encodeIdShort(newWoId, 'sitefotos_work_orders'),
      external_wo_id: external_wo_id,
      status_message: 'Work order received and queued for processing.',
  };
};


const updateClientWorkOrder = async (clientVendorId, encodedSitefotosWorkOrderId, updateData) => {
 
  const workOrderId = decodeIdShort(encodedSitefotosWorkOrderId);
  if (isNaN(workOrderId)) {
    throw new Error('Invalid sitefotos_work_order_id format after decoding.');
  }
  let wo = await awaitQuery(`select swo_form_id from sitefotos_work_orders where swo_id=?`,[workOrderId])
  
  const updateObject = {};
  if (updateData.status) {
    let internalStatusId;
    switch (updateData.status) {
      case 'SCHEDULED': internalStatusId = 1; break; // Example ID for 'SCHEDULED'
      case 'OPEN': internalStatusId = 2; break; // Example ID for 'OPEN'
      case 'COMPLETED': internalStatusId = 5; break; // Example ID for 'COMPLETED'
      case 'CANCELLED': internalStatusId = 6; break; // Example ID for 'CANCELLED'
      default: throw new Error(`Invalid status: ${updateData.status}`);
    }
    updateObject.swo_status = internalStatusId;
  }
  if (updateData.scheduled_date !== undefined) {
    updateObject.swo_schedule_datetime = updateData.scheduled_date;
  }
  if (updateData.external_wo_id !== undefined) {
    updateObject.swo_external_id = updateData.external_wo_id;
  }
  if (updateData.description !== undefined) {
    updateObject.swo_description = updateData.description;
  }

  if (Object.keys(updateObject).length === 0) {
    throw new Error('No valid fields provided for update.');
  }

  await updateObj('sitefotos_work_orders', updateObject, ['swo_id'], [workOrderId]);

  if (updateObject.swo_status && wo && wo.length) {
  
    if (updateObject.swo_status == 1 || updateObject.swo_status == 5 || updateObject.swo_status == 6) {
      const foObject = {
        sf_active: '3',
        sf_updated: Math.floor(new Date().getTime() / 1000)
      }
      await updateObj('sitefotos_forms', foObject, ['sf_id'], [wo[0]['swo_form_id']])
    }
    if(updateObject.swo_status == 2) {
      const foObject = {
        sf_active: '1',
        sf_updated: Math.floor(new Date().getTime() / 1000)
      }
      await updateObj('sitefotos_forms', foObject, ['sf_id'], [wo[0]['swo_form_id']])
    }
  }
  const updatedWoResult = await awaitSafeQuery(
    `SELECT wo.swo_id, wo.swo_external_id, swos.swos_status AS status_name, wo.swo_description, wo.swo_schedule_datetime 
     FROM sitefotos_work_orders wo
     LEFT JOIN sitefotos_work_orders_status swos ON wo.swo_status = swos.swos_id 
     WHERE wo.swo_id = ?`, 
    [workOrderId], {useMainPool: true}
  );

  if (!updatedWoResult.length) {
    throw new Error('Failed to retrieve updated work order.');
  }
  const updatedWo = updatedWoResult[0];

  return {
    sitefotos_work_order_id: encodeIdShort(updatedWo.swo_id, 'sitefotos_work_orders'), 
    external_wo_id: updatedWo.swo_external_id,
    status: updatedWo.status_name,
    description: updatedWo.swo_description,
    scheduled_date: updatedWo.swo_schedule_datetime,
  };
};

const listClientWorkOrders = async (clientVendorId, queryParams) => {
  const {
    page = 1,
    limit = 25,
    status,
    modified_at_from,
    modified_at_to,
    sitefotos_internal_vendor_id,
    sitefotos_internal_site_id,
    sort_by = 'last_modified',
    sort_order = 'desc',
  } = queryParams;

  const offset = (page - 1) * limit;
  const params = [clientVendorId];
  const countParams = [clientVendorId];

  let sql = `
    SELECT
      swo.swo_id,
      swo.swo_external_id,
      swos.swos_status AS status,
      swo.swo_description AS description,
      swo.swo_schedule_datetime AS scheduled_date,
      swo.swo_site_id,
      sscm.scs_client_internal_id AS primary_site_identifier, 
      st.st_trade AS trade_name,
      mv.vendor_company_name AS vendor_name,
      swo.swo_created_at,
      swo.swo_modified_at
    FROM sitefotos_work_orders swo
    LEFT JOIN sitefotos_work_orders_status swos ON swo.swo_status = swos.swos_id
    LEFT JOIN maptile_building mb ON swo.swo_site_id = mb.mb_id
    LEFT JOIN sitefotos_trades st ON swo.swo_trade_id = st.st_id
    LEFT JOIN maptile_vendors mv ON swo.swo_vendor_id = mv.vendor_id
    LEFT JOIN sitefotos_site_client_mapping_extended_ungrouped sscm ON swo.swo_site_id = sscm.sscm_site_id
    WHERE swo.swo_client_vendor_id = ?
  `;

  let countSql = `
    SELECT COUNT(*) as totalItems
    FROM sitefotos_work_orders swo
    LEFT JOIN sitefotos_work_orders_status swos ON swo.swo_status = swos.swos_id
    LEFT JOIN maptile_building mb ON swo.swo_site_id = mb.mb_id
    LEFT JOIN sitefotos_trades st ON swo.swo_trade_id = st.st_id
    LEFT JOIN maptile_vendors mv ON swo.swo_vendor_id = mv.vendor_id
    LEFT JOIN sitefotos_site_client_mapping_extended_ungrouped sscm ON swo.swo_site_id = sscm.sscm_site_id
    WHERE swo.swo_client_vendor_id = ?
  `;

  if (status) {
    sql += ` AND swos.swos_internal_status = ?`; // Assuming swos_internal_status holds 'SCHEDULED', 'OPEN', etc.
    countSql += ` AND swos.swos_internal_status = ?`;
    params.push(status);
    countParams.push(status);
  }
  if (modified_at_from) {
    sql += ` AND swo.swo_modified_at >= ?`;
    countSql += ` AND swo.swo_modified_at >= ?`;
    params.push(modified_at_from);
    countParams.push(modified_at_from);
  }
  if (modified_at_to) {
    sql += ` AND swo.swo_modified_at <= ?`;
    countSql += ` AND swo.swo_modified_at <= ?`;
    params.push(modified_at_to);
    countParams.push(modified_at_to);
  }
  if (sitefotos_internal_vendor_id) {
    const performingVendorDbId = decodeIdShort(sitefotos_internal_vendor_id);
    if (!isNaN(performingVendorDbId)) {
      sql += ` AND swo.swo_vendor_id = ?`;
      countSql += ` AND swo.swo_vendor_id = ?`;
      params.push(performingVendorDbId);
      countParams.push(performingVendorDbId);
    }
  }
  if (sitefotos_internal_site_id) {
    const siteDbId = decodeIdShort(sitefotos_internal_site_id);
    if (!isNaN(siteDbId)) {
      sql += ` AND swo.swo_site_id = ?`;
      countSql += ` AND swo.swo_site_id = ?`;
      params.push(siteDbId);
      countParams.push(siteDbId);
    }
  }

  // Sorting
  if (sort_by === 'last_modified') {
    sql += ` ORDER BY swo.swo_modified_at ${sort_order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC'}`;
  } else {
    // Default sort if sort_by is invalid or not 'last_modified'
    sql += ` ORDER BY swo.swo_modified_at DESC`;
  }
  
  sql += ` LIMIT ? OFFSET ?`;
  params.push(limit, offset);

  // Log queries and params before execution for debugging
  console.log("Executing SQL for work orders:", sql);
  console.log("Parameters for work orders SQL:", JSON.stringify(params, null, 2));
  console.log("Executing SQL for count:", countSql);
  console.log("Parameters for count SQL:", JSON.stringify(countParams, null, 2));

  const [workOrdersResult, countResult] = await Promise.all([
    awaitQuery(sql, params),
    awaitQuery(countSql, countParams)
  ]);
  
  const totalItems = countResult.length > 0 ? countResult[0].totalItems : 0;

  const mappedWorkOrders = workOrdersResult.map(wo => ({
    sitefotos_work_order_id: encodeIdShort(wo.swo_id, 'sitefotos_work_orders'),
    external_wo_id: wo.swo_external_id,
    status: wo.status,
    description: wo.description,
    scheduled_date: wo.scheduled_date,
    sitefotos_internal_site_id: encodeIdShort(wo.swo_site_id, 'maptile_building'),
    primary_site_identifier: wo.primary_site_identifier,
    trade_name: wo.trade_name,
    vendor_name: wo.vendor_name,
    // swo_created_at and swo_modified_at are not in the final client API doc, but were in the plan.
    // Omitting them here to match the client API doc. Add if needed.
  }));

  return { workOrders: mappedWorkOrders, totalItems };
};

const getWorkOrderByExternalId = async (clientVendorId, externalWoId) => {
  const sql = `
    SELECT
      swo.swo_id,
      swo.swo_external_id,
      swos.swos_status AS status,
      swo.swo_description AS description,
      swo.swo_schedule_datetime AS scheduled_date,
      swo.swo_site_id,
      sscm.scs_client_internal_id AS primary_site_identifier,
      st.st_trade AS trade_name,
      mv.vendor_company_name AS vendor_name
    FROM sitefotos_work_orders swo
    LEFT JOIN sitefotos_work_orders_status swos ON swo.swo_status = swos.swos_id
    LEFT JOIN maptile_building mb ON swo.swo_site_id = mb.mb_id
    LEFT JOIN sitefotos_trades st ON swo.swo_trade_id = st.st_id
    LEFT JOIN maptile_vendors mv ON swo.swo_vendor_id = mv.vendor_id
    LEFT JOIN sitefotos_site_client_mapping_extended_ungrouped sscm ON swo.swo_site_id = sscm.sscm_site_id
    WHERE swo.swo_client_vendor_id = ? AND swo.swo_external_id = ?
  `;

  const params = [clientVendorId, externalWoId];
  const result = await awaitQuery(sql, params);

  if (result.length === 0) {
    return null;
  }

  const wo = result[0];

  return {
    sitefotos_work_order_id: encodeIdShort(wo.swo_id, 'sitefotos_work_orders'),
    external_wo_id: wo.swo_external_id,
    status: wo.status,
    description: wo.description,
    scheduled_date: wo.scheduled_date,
    sitefotos_internal_site_id: encodeIdShort(wo.swo_site_id, 'maptile_building'),
    primary_site_identifier: wo.primary_site_identifier,
    trade_name: wo.trade_name,
    vendor_name: wo.vendor_name,
  };
};


const discoverWorkOrderFormTemplates = async (clientVendorId) => {
  const query = `
    SELECT sf_id, sf_form_name, sf_trade_id
    FROM sitefotos_forms
    WHERE sf_vendor_id = ? AND sf_form_type = 'workorderTemplate'
  `;
  const templates = await awaitSafeQuery(query, [clientVendorId]);
  return templates.map(template => ({
    template_id: encodeIdShort(template.sf_id, 'sitefotos_forms'),
    template_name: template.sf_form_name,
    trade_id: template.sf_trade_id,
  }));
};

const sendMessageToVendor = async (clientVendorId, messageData) => {
  const { sitefotos_work_order_id, external_wo_id, message } = messageData;

  let workOrderQuery;
  let queryParams;

  if (sitefotos_work_order_id) {
    // Query by internal work order ID
    const workOrderId = decodeIdShort(sitefotos_work_order_id);
    if (isNaN(workOrderId)) {
      throw new Error('Invalid sitefotos_work_order_id format.');
    }

    workOrderQuery = `
      SELECT
        swo.swo_id,
        swo.swo_external_id,
        swo.swo_vendor_id,
        mv.vendor_email,
        mv.vendor_company_name
      FROM sitefotos_work_orders swo
      LEFT JOIN maptile_vendors mv ON swo.swo_vendor_id = mv.vendor_id
      WHERE swo.swo_id = ? AND swo.swo_client_vendor_id = ?
    `;
    queryParams = [workOrderId, clientVendorId];
  } else {
    // Query by external work order ID
    workOrderQuery = `
      SELECT
        swo.swo_id,
        swo.swo_external_id,
        swo.swo_vendor_id,
        mv.vendor_email,
        mv.vendor_company_name
      FROM sitefotos_work_orders swo
      LEFT JOIN maptile_vendors mv ON swo.swo_vendor_id = mv.vendor_id
      WHERE swo.swo_external_id = ? AND swo.swo_client_vendor_id = ?
    `;
    queryParams = [external_wo_id, clientVendorId];
  }

  const workOrderResult = await awaitSafeQuery(workOrderQuery, queryParams);

  if (!workOrderResult || workOrderResult.length === 0) {
    throw new Error('Work order not found or not accessible by this client.');
  }

  const workOrder = workOrderResult[0];

  if (!workOrder.vendor_email) {
    throw new Error('Performing vendor email address not found.');
  }

  // Determine email recipients based on environment
  const isProduction = process.env.K8S_BRANCH === 'master';
  let recipientEmail;
  let recipientName;

  if (isProduction) {
    // Production: send to actual vendor
    recipientEmail = workOrder.vendor_email;
    recipientName = workOrder.vendor_company_name || 'Vendor';
  } else {
    // Non-production: send to development team
    recipientEmail = '<EMAIL>';
    recipientName = 'Development Team';
  }

  // Prepare email content
  const workOrderIdForSubject = sitefotos_work_order_id || external_wo_id;
  const subject = `Message for Work Order ${workOrderIdForSubject}`;

  // Prepare template data
  const templateData = {
    vendorName: recipientName,
    workOrderId: workOrderIdForSubject,
    message: message,
    isNonProduction: !isProduction
  };

  // Send email using template
  const emailResult = await sendEmailTemplateSES(
    recipientName,
    recipientEmail,
    subject,
    './static/emails/workorder/vendor-message-email.html',
    templateData,
    '<EMAIL>',
    'Sitefotos'
  );

  if (!emailResult || !emailResult.MessageId) {
    throw new Error('Failed to send email to performing vendor.');
  }

  // Return response
  return {
    sitefotos_work_order_id: sitefotos_work_order_id || encodeIdShort(workOrder.swo_id, 'sitefotos_work_orders'),
    external_wo_id: workOrder.swo_external_id,
    status_message: 'Message sent successfully to performing vendor.'
  };
};


module.exports = {
  discoverTrades,
  discoverServiceTypes,
  discoverVendors,
  discoverSites,
  createClientWorkOrder,
  updateClientWorkOrder,
  listClientWorkOrders, // Added new service function
  getWorkOrderByExternalId,
  discoverWorkOrderFormTemplates,
  sendMessageToVendor, // Added new service function
};

