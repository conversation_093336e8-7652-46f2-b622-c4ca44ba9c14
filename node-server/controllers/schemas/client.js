const { z } = require('zod');
const { awaitSafeQuery } = require('../../utils/db');
const { decodeIdShort } = require('../../utils/common');


const validateClientVendorAssociation = async (clientVendorId, vendorId) => {
  if (!vendorId) return true;
  const decodedVendorId = decodeIdShort(vendorId);
 if (!decodedVendorId) return false;
    
  const query = `select * from sitefotos_site_client_mapping_extended_ungrouped where scs_client_vendor_id = ? and sscm_vendor_id = ? limit 1`;
  const result = await awaitSafeQuery(query, [clientVendorId, decodedVendorId]);
  return result && result.length > 0;
}

const validateTrade = async(tradeId) => {
  if (!tradeId) return false;
  const query = `SELECT swot_id FROM sitefotos_work_orders_trades WHERE swot_id = ?`;
  const result = await awaitSafeQuery(query, [tradeId]);
  return result && result.length > 0;
}

const validateServiceTypeTrade = async(serviceTypeId, tradeId) => {
  if (!tradeId) return false;
  if (!serviceTypeId) return false;
  const query = `SELECT sst_id FROM sitefotos_service_types WHERE sst_id = ? and sst_trade_id = ?`;
  const result = await awaitSafeQuery(query, [serviceTypeId, tradeId]);
  return result && result.length > 0;
}

const validateWorkOrderAccess = async (clientVendorId, workOrderId) => {
  
  if (isNaN(workOrderId)) return false;
  const query = `
    SELECT wo.swo_id
    FROM sitefotos_work_orders wo
    WHERE wo.swo_id = ? AND wo.swo_client_vendor_id = ?;
  `;
  const result = await awaitSafeQuery(query, [workOrderId, clientVendorId]);
  return result && result.length > 0;
};

// New validation function for site association
const validateClientSiteAssociation = async (clientVendorId, siteId, performingVendorId = null) => {
  if (!siteId) return true; // If no siteId is provided, skip validation for this part
  const decodedSiteId = decodeIdShort(siteId);
  if (!decodedSiteId) return false;

  let query = `
    SELECT sscm.sscm_client_site_id
    FROM sitefotos_site_client_mapping sscm
    JOIN sitefotos_client_sites scs ON sscm.sscm_client_site_id = scs.scs_id
    WHERE scs.scs_client_vendor_id = ? AND sscm.sscm_site_id = ?
  `;
  const params = [clientVendorId, decodedSiteId];

  if (performingVendorId) {
    const decodedPerformingVendorId = decodeIdShort(performingVendorId);
    if (!decodedPerformingVendorId) return false; // Invalid performing vendor ID format
    query += ` AND sscm.sscm_vendor_id = ?`;
    params.push(decodedPerformingVendorId);
  }
  query += ` LIMIT 1`;

  const result = await awaitSafeQuery(query, params);
  return result && result.length > 0;
};

const validateFormTemplate = async (formTemplateId) => {
    if (!formTemplateId) return true;
    const decodedFormTemplateId = decodeIdShort(formTemplateId);
    if (!decodedFormTemplateId) return false;
    const query = `SELECT sf_id FROM sitefotos_forms WHERE sf_id = ?`;
    const result = await awaitSafeQuery(query, [decodedFormTemplateId]);
    return result && result.length > 0;
};

const siteDiscoveryQuerySchema = z.object({
  sitefotos_internal_vendor_id: z.string().optional(),
});

const refinesiteDiscoveryQuerySchemaAPI = async (val, ctx, clientVendorId) => {
    if (val.sitefotos_internal_vendor_id) {
        const isValidVendor = await validateClientVendorAssociation(clientVendorId, val.sitefotos_internal_vendor_id);
        if (!isValidVendor) { // Changed variable name for clarity
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: "Invalid or unassociated performing vendor ID provided.",
                path: ["sitefotos_internal_vendor_id"],
            });
        }
    }
};

const siteDiscoveryQuerySchemaAPI = (clientVendorId) =>
  siteDiscoveryQuerySchema.strict().superRefine(async (val, ctx) => {
      await refinesiteDiscoveryQuerySchemaAPI(val, ctx, clientVendorId);
  });
const createWorkOrderServiceSchema = z.object({
  service_name: z.string(),
  service_type_id: z.number().int(),
});

const createWorkOrderDetailsSchema = z.object({
  external_wo_id: z.string(),
  description: z.string().optional(),
  trade_id: z.number().int(),
  scheduled_date: z.number().int().optional().default(Math.floor(Date.now() / 1000)),
  services: z.array(createWorkOrderServiceSchema).optional(),
  close_on_submit: z.boolean().optional().default(false),
  form_template_id: z.string().optional(),
}).superRefine((data, ctx) => {
    if (!data.form_template_id && (!data.services || data.services.length === 0)) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Services are required when no form template is provided.",
            path: ["services"],
        });
    }
});

const createWorkOrderRequestSchema = z.object({
  sitefotos_internal_vendor_id: z.string(),
  sitefotos_internal_site_id: z.string(), 
  work_order_details: createWorkOrderDetailsSchema,
});

const validateExternalWorkOrderIdUniqueness = async (clientVendorId, performingVendorId, externalWoId) => {
  if (!externalWoId) return true;
  const query = `
    SELECT wo.swo_id
    FROM sitefotos_work_orders wo
    WHERE wo.swo_external_id = ? AND wo.swo_client_vendor_id = ? AND wo.swo_vendor_id = ?;
  `;
  const result = await awaitSafeQuery(query, [externalWoId, clientVendorId, performingVendorId]);
  return result && result.length === 0; // Return true if no existing work order found
};

const validateCreateWorkOrderRequestSchema = async (val, ctx, clientVendorId) => {
  if (val.sitefotos_internal_vendor_id) {
    const isValid = await validateClientVendorAssociation(clientVendorId, val.sitefotos_internal_vendor_id);
    if (!isValid) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Invalid vendor ID",
        path: ["sitefotos_internal_vendor_id"],
      });
    }
  }

  // Validate external work order ID uniqueness
  if (val.work_order_details && val.work_order_details.external_wo_id && val.sitefotos_internal_vendor_id) {
    const performingVendorDbId = decodeIdShort(val.sitefotos_internal_vendor_id);
    if (!isNaN(performingVendorDbId)) {
      const isUnique = await validateExternalWorkOrderIdUniqueness(clientVendorId, performingVendorDbId, val.work_order_details.external_wo_id);
      if (!isUnique) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "External work order ID already exists for this client and performing vendor combination",
          path: ["work_order_details", "external_wo_id"],
        });
      }
    }
  }

  // Validate that the site belongs to the client vendor (and optionally to the performing vendor)
  if (val.sitefotos_internal_site_id) {
    const isValid = await validateClientSiteAssociation(clientVendorId, val.sitefotos_internal_site_id, val.sitefotos_internal_vendor_id);
    if (!isValid) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Invalid or unassociated site ID for the client or specified performing vendor",
        path: ["sitefotos_internal_site_id"],
      });
    }
  }

  if (val.work_order_details.trade_id) {
    const isValid = await validateTrade(val.work_order_details.trade_id);
    if (!isValid) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Invalid trade ID",
        path: ["work_order_details", "trade_id"],
      });
    }
  }
  if (val.work_order_details.services) {
    for (let service of val.work_order_details.services) {
      if (service.service_type_id) {
        const isValid = await validateServiceTypeTrade(service.service_type_id, val.work_order_details.trade_id);
        if (!isValid) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Invalid service type ID for the specified trade",
            path: ["work_order_details", "services"],
          });
        }
      }
    }
  }

  if (val.work_order_details.form_template_id) {
    const isValid = await validateFormTemplate(val.work_order_details.form_template_id);
    if (!isValid) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Invalid form template ID",
        path: ["work_order_details", "form_template_id"],
      });
    }
  }
}

const createWorkOrderRequestSchemaAPI = (clientVendorId) => 
  createWorkOrderRequestSchema.strict().superRefine(async (val, ctx) => {
    await validateCreateWorkOrderRequestSchema(val, ctx, clientVendorId);
  });


const updateWorkOrderRequestSchema = z.object({
  status: z.enum(['SCHEDULED', 'OPEN', 'COMPLETED', 'CANCELLED']).optional(),
  scheduled_date: z.number().int().optional(), 
  external_wo_id: z.string().optional(),
  description: z.string().optional(),
})
.refine(
  (data) => Object.keys(data).length > 0,
  { message: "At least one field must be provided for update" }
);

// Schema factory for updateWorkOrder to include async validation for work order access
const updateWorkOrderRequestSchemaAPI = (clientVendorId, workOrderId) => {
  // Define the base object schema structure.
  // This mirrors the object definition used in `updateWorkOrderRequestSchema`.
  const baseObjectDefinition = {
    status: z.enum(['SCHEDULED', 'OPEN', 'COMPLETED', 'CANCELLED']).optional(),
    scheduled_date: z.number().int().optional(),
    external_wo_id: z.string().optional(),
    description: z.string().optional(),
  };

  return z.object(baseObjectDefinition) // Create the ZodObject
    .strict() // Apply .strict() to the ZodObject
    .refine( // Apply the synchronous refinement (at least one field must be provided)
      (data) => Object.keys(data).length > 0,
      { message: "At least one field must be provided for update" }
    )
    .superRefine(async (val, ctx) => { // Apply the asynchronous superRefine for access validation
      const decodedWorkOrderId = decodeIdShort(workOrderId); // Assuming workOrderId from params is encoded
      if (isNaN(decodedWorkOrderId)) {
          ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: "Invalid sitefotos_work_order_id format.",
              path: [], // Path to the parameter, not a body field
          });
          return; // Stop further validation if ID is invalid
      }

      const hasAccess = await validateWorkOrderAccess(clientVendorId, decodedWorkOrderId);
      if (!hasAccess) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Work order not found or not accessible by this client.",
          path: [], // Path to the parameter
        });
      }
      // Validate external work order ID uniqueness if being updated
      if (val.external_wo_id) {
        // Get the performing vendor ID and current external_wo_id from the existing work order
        const existingWoQuery = `
          SELECT wo.swo_vendor_id, wo.swo_external_id
          FROM sitefotos_work_orders wo
          WHERE wo.swo_id = ? AND wo.swo_client_vendor_id = ?;
        `;
        const existingWoResult = await awaitSafeQuery(existingWoQuery, [decodedWorkOrderId, clientVendorId]);

        if (existingWoResult && existingWoResult.length > 0) {
          const performingVendorId = existingWoResult[0].swo_vendor_id;
          const currentExternalId = existingWoResult[0].swo_external_id;

          // Only check uniqueness if the external_wo_id is actually changing
          if (val.external_wo_id !== currentExternalId) {
            const isUnique = await validateExternalWorkOrderIdUniqueness(clientVendorId, performingVendorId, val.external_wo_id);
            if (!isUnique) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: "External work order ID already exists for this client and performing vendor combination",
                path: ["external_wo_id"],
              });
            }
          }
        }
      }

      // Add other field-specific async validations here if needed in the future
    });
};


const updatedWorkOrderSummarySchema = z.object({
    sitefotos_work_order_id: z.string(),
    external_wo_id: z.string().optional(),
    status: z.string().optional(),
    description: z.string().optional(),
    scheduled_date: z.number().int().optional(),
});




const allWebhookEventsEnum = z.enum([
  'checkin',
  'checkout',
  'workorders_updated',
  'client_checkin',
  'client_checkout',
  'client_wo_submission',
]);


const createWebhookSubscriptionRequestSchema = z.object({
  url: z.string().url(),
  event: allWebhookEventsEnum,
});

const createWebhookSubscriptionResponseSchema = z.object({
  webhook_id: z.string(), 
});


const webhookSubscriptionSchema = z.object({
  webhook_id: z.string(), 
  url: z.string().url(),
  event: allWebhookEventsEnum,
});

const listWebhookSubscriptionsResponseSchema = z.array(webhookSubscriptionSchema);


const updateWebhookSubscriptionRequestSchema = z.object({
  url: z.string().url(),
  event: allWebhookEventsEnum,
});


const updateWebhookSubscriptionResponseSchema = createWebhookSubscriptionResponseSchema;




const outboundWebhookLocationSchema = z.object({
  latitude: z.number(),
  longitude: z.number(),
});

const outboundClientCheckInEventDataSchema = z.object({
  location: outboundWebhookLocationSchema.optional(), 
});

const outboundClientCheckOutEventDataSchema = z.object({
  location: outboundWebhookLocationSchema.optional(),
});

const outboundClientWOSubmissionServiceSchema = z.object({
  service_name: z.string(),
  status: z.string(), 
});

const outboundClientWOSubmissionPhotoSchema = z.object({
  photo_url: z.string().url()
});

const outboundClientWOSubmissionEventDataSchema = z.object({
  services: z.array(outboundClientWOSubmissionServiceSchema),
  photos: z.array(outboundClientWOSubmissionPhotoSchema),
});

const baseOutboundWebhookPayloadSchema = z.object({
  event: allWebhookEventsEnum, 
  event_timestamp: z.number().int(), 
  sitefotos_work_order_id: z.string(), 
  external_wo_id: z.string(),
  sitefotos_internal_site_id: z.string(), 
  sitefotos_internal_vendor_id: z.string(), 
  primary_site_identifier: z.string(),
});


const outboundWebhookPayloadSchema = z.discriminatedUnion("event", [
  baseOutboundWebhookPayloadSchema.extend({
    event: z.literal("client_checkin"),
    event_data: outboundClientCheckInEventDataSchema,
  }),
  baseOutboundWebhookPayloadSchema.extend({
    event: z.literal("client_checkout"),
    event_data: outboundClientCheckOutEventDataSchema,
  }),
  baseOutboundWebhookPayloadSchema.extend({
    event: z.literal("client_wo_submission"),
    event_data: outboundClientWOSubmissionEventDataSchema,
  }),
]);

// Base schema for listWorkOrders query parameters
const listWorkOrdersQueryBaseSchema = z.object({
  page: z.string().optional().default('1').pipe(z.coerce.number().int().min(1)),
  limit: z.string().optional().default('25').pipe(z.coerce.number().int().min(1).max(100)),
  status: z.enum(['SCHEDULED', 'OPEN', 'COMPLETED', 'CANCELLED']).optional(),
  modified_at_from: z.string().optional().pipe(z.coerce.number().int().positive().optional()),
  modified_at_to: z.string().optional().pipe(z.coerce.number().int().positive().optional()),
  sitefotos_internal_vendor_id: z.string().optional(),
  sitefotos_internal_site_id: z.string().optional(),
  sort_by: z.enum(['last_modified']).optional().default('last_modified'),
  sort_order: z.enum(['asc', 'desc']).optional().default('desc'),
});

// Refine function for listWorkOrdersQuerySchemaAPI
const refineListWorkOrdersQuery = async (val, ctx, clientVendorId) => {
  if (val.sitefotos_internal_vendor_id) {
    const isValidPerformingVendor = await validateClientVendorAssociation(clientVendorId, val.sitefotos_internal_vendor_id);
    if (!isValidPerformingVendor) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Invalid or unassociated performing vendor ID (sitefotos_internal_vendor_id).",
        path: ["sitefotos_internal_vendor_id"],
      });
    }
  }
  if (val.sitefotos_internal_site_id) {
    // Pass the performing vendor ID if available, to ensure the site is also linked to that specific performing vendor if filtered
    const isValidSite = await validateClientSiteAssociation(clientVendorId, val.sitefotos_internal_site_id, val.sitefotos_internal_vendor_id);
    if (!isValidSite) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Invalid or unassociated site ID (sitefotos_internal_site_id) for the client or specified performing vendor.",
        path: ["sitefotos_internal_site_id"],
      });
    }
  }
  // Add other cross-field validations if necessary, e.g., modified_at_from <= modified_at_to
  if (val.modified_at_from && val.modified_at_to && val.modified_at_from > val.modified_at_to) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "modified_at_from cannot be after modified_at_to.",
      path: ["modified_at_from"], // Or a general path
    });
  }
};

// Schema factory for listWorkOrders
const listWorkOrdersQuerySchemaAPI = (clientVendorId) =>
  listWorkOrdersQueryBaseSchema.strict().superRefine(async (val, ctx) => {
    await refineListWorkOrdersQuery(val, ctx, clientVendorId);
  });

const getWorkOrderByExternalIdSchema = z.object({
  external_wo_id: z.string(),
});

const validateWorkOrderAccessByExternalId = async (clientVendorId, externalWoId) => {
  if (!externalWoId) return false;
  const query = `
    SELECT wo.swo_id
    FROM sitefotos_work_orders wo
    WHERE wo.swo_external_id = ? AND wo.swo_client_vendor_id = ?;
  `;
  const result = await awaitSafeQuery(query, [externalWoId, clientVendorId]);
  return result && result.length > 0;
};

const getWorkOrderByExternalIdSchemaAPI = (clientVendorId) =>
  getWorkOrderByExternalIdSchema.strict().superRefine(async (val, ctx) => {
    const hasAccess = await validateWorkOrderAccessByExternalId(clientVendorId, val.external_wo_id);
    if (!hasAccess) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Work order not found or not accessible by this client.",
        path: ["external_wo_id"],
      });
    }
  });

// Schema for sending message to vendor
const sendMessageToVendorSchema = z.object({
  sitefotos_work_order_id: z.string().optional(),
  external_wo_id: z.string().optional(),
  message: z.string().min(1, "Message cannot be empty"),
}).refine(
  (data) => data.sitefotos_work_order_id || data.external_wo_id,
  {
    message: "Either sitefotos_work_order_id or external_wo_id must be provided",
    path: ["sitefotos_work_order_id", "external_wo_id"],
  }
);

const validateSendMessageToVendorAccess = async (val, ctx, clientVendorId) => {
  if (val.sitefotos_work_order_id) {
    // Validate access using internal work order ID
    const decodedWorkOrderId = decodeIdShort(val.sitefotos_work_order_id);
    if (isNaN(decodedWorkOrderId)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Invalid sitefotos_work_order_id format.",
        path: ["sitefotos_work_order_id"],
      });
      return;
    }

    const hasAccess = await validateWorkOrderAccess(clientVendorId, decodedWorkOrderId);
    if (!hasAccess) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Work order not found or not accessible by this client.",
        path: ["sitefotos_work_order_id"],
      });
    }
  } else if (val.external_wo_id) {
    // Validate access using external work order ID
    const hasAccess = await validateWorkOrderAccessByExternalId(clientVendorId, val.external_wo_id);
    if (!hasAccess) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Work order not found or not accessible by this client.",
        path: ["external_wo_id"],
      });
    }
  }
};

const sendMessageToVendorSchemaAPI = (clientVendorId) =>
  sendMessageToVendorSchema.strict().superRefine(async (val, ctx) => {
    await validateSendMessageToVendorAccess(val, ctx, clientVendorId);
  });


module.exports = {
  siteDiscoveryQuerySchema,
  createWorkOrderRequestSchema,
  updateWorkOrderRequestSchema,
  updatedWorkOrderSummarySchema,
  createWebhookSubscriptionRequestSchema,
  createWebhookSubscriptionResponseSchema,
  listWebhookSubscriptionsResponseSchema,
  webhookSubscriptionSchema,
  updateWebhookSubscriptionRequestSchema,
  updateWebhookSubscriptionResponseSchema,
  outboundWebhookPayloadSchema,
  allWebhookEventsEnum,
  siteDiscoveryQuerySchemaAPI,
  createWorkOrderRequestSchemaAPI,
  updateWorkOrderRequestSchemaAPI, // Export the new schema factory
  listWorkOrdersQuerySchemaAPI, // Added new schema
  getWorkOrderByExternalIdSchemaAPI,
  sendMessageToVendorSchemaAPI, // Added new schema for sending messages to vendors
};