const express = require('express')
const path = require('path');
const contactController = require('../controllers/api/contacts-controller')
const sitesController = require('../controllers/api/sites-controller')
const accountController = require('../controllers/api/account-controller')
const formsController = require('../controllers/api/forms-controller')
const mapsController = require('../controllers/api/maps-controller')
const tradesController = require('../controllers/api/trades-controller')
const workorderController = require('../controllers/api/workorder-controller')
const refrenceController = require('../controllers/api/reference-controller')
const fleetController = require('../controllers/api/fleet-controller')
const serviceController = require('../controllers/api/services-controller')
const routesController = require('../controllers/api/routes-controller')
const reportController = require('../controllers/api/report-controller')
const webhookController = require('../controllers/api/webhook-controller')
const pricingController = require('../controllers/api/pricing-controller')
const pricingControllerV2 = require('../controllers/api/v2/pricing-controller');
const photosController = require('../controllers/api/photos-controller');
const materialsController = require('../controllers/api/materials-controller');
const clientControllerV2 = require('../controllers/api/v2/client-controller');
const servicesControllerV2 = require('../controllers/api/v2/services-controller');

const router = express.Router()
let openAPIJSONSpecs = null
// Documentation OpenAPI JSON file.
router.get('/referenceJSON', async (req, res, next) => {
    // NOTE: Saving JSON file to global var so it does not generate docs on every request and JSON can be servered from var.
    if (openAPIJSONSpecs !== null) {
        res.status(200).json(openAPIJSONSpecs)
    } else {
        openAPIJSONSpecs = refrenceController.refernceJSON();
        if (openAPIJSONSpecs !== null) {
            res.status(201).json(openAPIJSONSpecs)
        } else {
            res.status(400).json({'message': 'Unable to generate reference.'});
        }
    }
});


const sitesControllerV2 = require('../controllers/api/v2/sites-controller')

// V2 Site routes
router.get('/sites', sitesControllerV2.listSites)
router.post('/sites', sitesControllerV2.createSite)
router.get('/sites/:site_id', sitesControllerV2.getSite)
router.patch('/sites/:site_id', sitesControllerV2.updateSite)
router.post('/sites/:site_id/maps', sitesControllerV2.createSiteMap); // New endpoint for creating a site map
router.get('/property-types', sitesControllerV2.getPropertyTypes)
router.get('/zones', sitesControllerV2.getZones)

const usersControllerV2 = require('../controllers/api/v2/users-controller')
router.get('/users', usersControllerV2.listUsers)
router.get('/users/:user_id', usersControllerV2.getUser)
router.post('/users', usersControllerV2.createUser)
router.get('/roles', usersControllerV2.getRoles)
router.get('/pages', usersControllerV2.getPages)
router.patch('/users/:user_id', usersControllerV2.updateUser)

const serviceHistoryController = require('../controllers/api/service-history-controller')
router.get('/service-history', serviceHistoryController.getServiceHistory)
router.get('/service-history/:workorder_id', serviceHistoryController.getServiceHistoryWorkorder)
router.patch('/service-history/:service_id', serviceHistoryController.updateServiceMetadata)

const contactsController = require('../controllers/api/v2/contacts-controller')
router.get('/employees', contactsController.listEmployees)
router.get('/employees/:employee_id', contactsController.getEmployee)
router.post('/employees', contactsController.createEmployee)
router.patch('/employees/:employee_id', contactsController.updateEmployee)
router.get('/companies', contactsController.listCompanies)
router.get('/companies/:company_id', contactsController.getCompany)
router.post('/companies', contactsController.createCompany)
router.patch('/companies/:company_id', contactsController.updateCompany)
router.get('/clients', contactsController.listClients)
router.get('/clients/:client_id', contactsController.getClient)
router.post('/clients', contactsController.createClient)
router.patch('/clients/:client_id', contactsController.updateClient)
router.get('/contractors', contactsController.listContractors)
router.get('/contractors/:contractor_id', contactsController.getContractor)
router.post('/contractors', contactsController.createContractor)
router.patch('/contractors/:contractor_id', contactsController.updateContractor)
router.get('/branches', contactsController.listBranches)
router.get('/branches/:branch_id', contactsController.getBranch)
router.post('/branches', contactsController.createBranch)
router.patch('/branches/:branch_id', contactsController.updateBranch)
router.post('/contacts/:contact_id/resend-welcome-email', contactsController.resendWelcomeEmail)

// Documentation.
router.get('/', function(req, res){
    res.redirect('/v1/api/reference');
 });
router.get('/reference', async (req, res) => {
    res.sendFile(path.join(__dirname, '../static/api-reference.html'))
})

// Account resource API's
router.get('/account/authenticate', accountController.authenticate)
router.get('/auth',accountController.authenticate);
router.get('/account/vendors', accountController.getVendors);
router.post('/account/provisionApiKey', accountController.provisionApiKey)
router.post('/provisionApiKey', accountController.provisionApiKey);

// Contact API's

// Employee resource API's
router.get('/employee', contactController.getEmployees)
router.get('/employee/:id', contactController.getEmployeeById)
router.get('/getEmployees',contactController.getEmployees)

router.post('/employee', contactController.createEmployee)
router.post('/createEmployee',contactController.createEmployee);

router.patch('/employee', contactController.updateEmployee)
router.post('/updateEmployee',contactController.updateEmployee);

router.delete('/employee', contactController.deleteEmployee)
router.delete('/deleteEmployee',contactController.deleteEmployee);

// Client resource API's
router.get('/client', contactController.getClients)
router.get('/client/:id', contactController.getClientById)
router.get('/getClients',contactController.getClients);

router.post('/client', contactController.createClient)
router.post('/createClient',contactController.createClient);

router.patch('/client', contactController.updateClient)
router.post('/updateClient',contactController.updateClient);

router.delete('/client', contactController.deleteClient)
router.delete('/deleteClient',contactController.deleteClient);

// Contractor resource API's

router.get('/contractor', contactController.getContractors)
router.get('/contractor/:id', contactController.getContractorById)
router.get('/getContractors',contactController.getContractors);

router.post('/contractor', contactController.createContractor)
router.post('/createContractor',contactController.createContractor);

router.patch('/contractor', contactController.updateContractor)
router.patch('/contractor/:contact_id', contactController.updateContractorById)
router.post('/updateContractor',contactController.updateContractor);

router.delete('/contractor', contactController.deleteContractor)
router.delete('/deleteContractor',contactController.deleteContractor);


// Building resource API's

router.get('/site', sitesController.getSites);
router.get('/site/:id', sitesController.getSitesById);
router.get('/siteByExId', sitesController.getSitesByExternalId);
router.get('/sites/verizon-sites-user', sitesController.getVerizonSitesUser);
router.post('/sites/verizon-sites-user', sitesController.setVerizonSitesUser);
router.get('/sites/verizon-sites', sitesController.getVerizonSites);
router.get('/site/sessions', sitesController.getSitesSessions);
router.get('/getBuildings',sitesController.getSites);

router.post('/site', sitesController.createSite)
router.post('/createBuilding',sitesController.createSite);

router.patch('/site', sitesController.updateSite)
router.post('/updateBuilding',sitesController.updateSite);

router.delete('/site', sitesController.deleteSite)
router.delete('/deleteBuilding',sitesController.deleteSite);

router.post('/sites/update-site-details', sitesController.updateSiteDetails);
// Map resource API's
router.get('/map', mapsController.getMaps)
router.get('/getMaps',mapsController.getMaps);
router.post('/find-maps', mapsController.findMapLayers);
router.post('/map/measurements', mapsController.mapMeasurements);
router.post('/getMeasurements', mapsController.mapMeasurements);
router.post('/map/measurement-summary', mapsController.measurementSummary);
router.post('/measurements', mapsController.mapMeasurements2);
router.post('/update-map-details', mapsController.updateMapDetails);
router.post('/hires-square-footage', mapsController.mapHiResSquareFootage);

// router.post('/map/measurements', mapsController.mapCalculations);
router.post('/getMapCalculations', mapsController.mapCalculations);

// Trade resource API's
router.get('/trade', tradesController.getTrades)
router.get('/getTrades',tradesController.getTrades);

// Form resource API's
router.get('/form', formsController.getForms)
router.get('/getForms', formsController.getForms);
router.get('/form/submission/:form_submission_id', formsController.getFormSubmissionBySubmissionID)
router.post('/form/submit', formsController.submitForm);

//Submitted forms.
router.get('/form/submitted', formsController.getSubmittedForms2)
router.get('/form/submissions', formsController.getSubmittedForms2);
router.get('/form/submission-data', formsController.getSubmittedForms3);
router.get('/form/submitted/:form_id', formsController.getSubmittedFormsByID)


//Routes API's
router.get('/routes', routesController.getRoutes)
router.patch('/routes', routesController.editRoute)
router.post('/routes', routesController.createRoute)
router.get('/routes/:route_id', routesController.getRoutesByID)
router.delete('/routes/:id', routesController.deactivateRoute);

//Workorder
router.post('/workorder',workorderController.createWorkOrder);
router.post('/createWorkorder',workorderController.createWorkOrder);
router.patch('/workorder',workorderController.editWorkOrder);
router.get('/workorder/systems', workorderController.getSystems)
router.get('/workorder/external/:system_id/:external_id', workorderController.getWorkorderByExternalId)
router.get('/workorder/status', workorderController.getStatus)
router.post('/workorder/system/:system_id/refresh', workorderController.refreshSystem)
router.post('/workorder/integration', workorderController.integrate)
router.post('/workorder/document',workorderController.createWorkOrderDocument);
router.get('/workorder/formtemplate',workorderController.getTemplateForms);
router.put('/workorder/assignment/:workorder_id', workorderController.changeAssignmentWorkOrder)
router.post('/workorder/external/:system_id', workorderController.parseExternalSubmission)
router.put('/workorder/assignment/external/:system_id/:external_id', workorderController.changeAssignmentWorkOrderExternal)
router.get('/workorder', workorderController.listWorkorders);
router.patch('/workorder/:workorder_id', workorderController.patchWorkorder);
router.post('/workorder/fiwo', workorderController.addFIWO);
router.patch('/workorder/fiwo', workorderController.modifyFIWO);
router.delete('/workorder/fiwo', workorderController.deleteFIWO);
router.patch('/workorder/:workorder_id/status', workorderController.updateInternalWorkOrderStatus); // New route for internal status update

//Mandatory Open Work Orders
router.post('/workorder/mowo/:system_id/:external_id', workorderController.forceOpen)
router.delete('/workorder/mowo/:system_id/:workorder_id', workorderController.forceClose)
router.get('/workorder/mowo', workorderController.listForceOpenWorkOrders)
//Fleet
router.get('/fleet/timing', fleetController.buildingTimes);

// Service routes
router.get('/service', serviceController.getServices);
router.get('/service/:serviceId', serviceController.getServiceById);
router.post('/service', serviceController.createService);
router.put('/service/:serviceId', serviceController.updateService);
router.delete('/service/:serviceId', serviceController.deleteService);
router.get('/service/service-levels', serviceController.getServiceLevels);

// Trade routes
router.get('/trade', serviceController.getTrades);

// Service Type routes
router.get('/service-type', serviceController.getServiceTypes);

// Equipment routes
router.get('/equipment', serviceController.getEquipment);

// Material routes
router.get('/material', serviceController.getMaterials);

//reports
router.get('/reports/service-tracker-sites', reportController.serviceTrackerSites)
router.get('/reports/service-tracker-sites-client-model', reportController.serviceTrackerSitesClientModel)
router.get('/reports/check-in-out-time-range-client-view', reportController.getCheckInOutTimeRangeClientView)
//Webhook
router.post('/webhook', webhookController.addWebhook)
router.get('/webhook', webhookController.getWebhooks)
router.patch('/webhook/:id', webhookController.updateWebhook)
router.delete('/webhook/:id', webhookController.deleteWebhook)

//Pricing Contracts


router.get('/pricing-contracts', pricingControllerV2.getPricingContracts); //Converted to V2 Pricing
router.get('/pricing-contracts/units', pricingControllerV2.getPricingUnits);
router.get('/pricing-contracts/service-levels', pricingControllerV2.getServiceLevels);
router.get('/pricing-contracts/:id', pricingControllerV2.getPricingContractById); //Converted to V2 Pricing
router.post('/pricing-contracts', pricingControllerV2.addPricingContractViaApi); //Converted to V2 Pricing
router.delete('/pricing-contracts/:id', pricingControllerV2.deletePricingContract); //Converted to V2 Pricing
router.patch('/pricing-contracts/:id', pricingControllerV2.patchPricingContract);
router.post('/pricing-contracts/:id/line-items', pricingControllerV2.addLineItem);
router.patch('/pricing-contracts/:id/line-items/:line_item_id', pricingControllerV2.updateLineItem);
router.delete('/pricing-contracts/:id/line-items/:line_item_id', pricingControllerV2.deleteLineItem);
router.post('/add-admin-pricing', pricingController.addAdminPricing); //This we do not need to give to clients.

//Photos Api
router.get('/photos', photosController.getAllPhotosPaginated);
router.get('/photos/daterange', photosController.getPhotosInDateRange);
router.get('/photos/site/:buildingId', photosController.getPhotosForBuilding); //Should this be paginated as well?
router.get('/photos/form/:formId', photosController.getPhotosForForm); //Should this be paginated as well?

//Materials Api
router.get('/materials/:id', materialsController.getMaterialById);
router.get('/materials', materialsController.getMaterials);
router.post('/materials', materialsController.addMaterial);
router.delete('/materials/:id', materialsController.deleteMaterial);


const materialsControllerV2 = require('../controllers/api/v2/materials-controller');
router.get('/materials2', materialsControllerV2.listMaterials);
router.get('/materials2/:material_id', materialsControllerV2.getMaterial);
router.post('/materials2', materialsControllerV2.createMaterial);
router.patch('/materials2/:material_id', materialsControllerV2.updateMaterial);


const formsControllerV2 = require('../controllers/api/v2/forms-controller');
router.post('/forms/submit', formsControllerV2.submitForm);
router.get('/forms', formsControllerV2.listForms);
router.post('/forms', formsControllerV2.submitSimplifiedForm);
router.get('/forms/submission/:form_submission_id', formsControllerV2.getFormSubmission);

// V2 Client API routes
router.get('/client-view/vendors', clientControllerV2.discoverVendors);
router.get('/client-view/sites', clientControllerV2.discoverSites);
router.get('/client-view/trades', clientControllerV2.discoverTrades);
router.get('/client-view/service-types', clientControllerV2.discoverServiceTypes);
router.get('/client-view/workorder-form-templates', clientControllerV2.discoverWorkOrderFormTemplates);
router.post('/client-view/workorders', clientControllerV2.createWorkOrder);
router.patch('/client-view/workorders/:sitefotos_work_order_id', clientControllerV2.updateWorkOrder);
router.get('/client-view/workorders', clientControllerV2.listWorkOrders);
router.get('/client-view/workorders/by-external-id/:external_wo_id', clientControllerV2.getWorkOrderByExternalId);
router.post('/client-view/workorders/send-message', clientControllerV2.sendMessageToVendor);

// V2 Services API routes
router.get('/services/trades', servicesControllerV2.discoverTrades);
router.get('/services/service-types', servicesControllerV2.discoverServiceTypes);


module.exports = router
